-- ========================================
-- Usuario do AD
-- ========================================

(
    ISNULL(CAST(EntryDate AS DATE), '1899-12-30 00:00:00.000') <= CAST(GETDATE() AS DATE)
    OR
    (
        EXISTS
        (
            SELECT 1
            FROM (
                SELECT UID_Person
                FROM ADSAccount
                WHERE 1 = 1
            ) AS X
            WHERE X.UID_Person = Person.UID_Person
        )
    )
)
AND (isexternal = 0)
AND (IdentityType = N'Primary')
AND (UID_FirmPartner = '914e450a-2ed3-42e8-930c-57d2b89598fa')

-- ========================================
-- Usuario do AD com conta T
-- ========================================

(
    ISNULL(CAST(EntryDate AS DATE), '1899-12-30 00:00:00.000') <= CAST(GETDATE() AS DATE)
    OR
    (
        EXISTS
        (
            SELECT 1
            FROM (
                SELECT UID_Person
                FROM ADSAccount
                WHERE 1 = 1
            ) AS X
            WHERE X.UID_Person = Person.UID_Person
        )
    )
)
AND (isexternal = 1)
AND (IdentityType = N'Primary')
AND (isinactive = '0')
AND (UID_FirmPartner = '914e450a-2ed3-42e8-930c-57d2b89598fa')
