If Not CBool(Variables("FULLSYNC")) Then
	Select Case ($FK(UID_TSBBehavior).ITDataUsage:Int$)
		Case 0:'Unmanaged (do not get data from employee)
#If EX2K Then
			
			Dim mailaddress As String = $Mail$
			
			If  mailaddress="" AndAlso $SAMAccountName$<>"" Then 
				'if no mailaddress is set manually, generate
				Dim account As String = VI_AE_FormatConvertUmlaut_Sonderzeichen($SAMAccountName$)
				Dim maildom As String = Connection.GetConfigParm("QER\Person\DefaultMailDomain")
				
				If maildom <> "" Then
                	 ' default mail domain has to be defined, see configuration parameter 'QER\Person\DefaultMailDomain'
            		If Not maildom.StartsWith("@") Then
                    	maildom = "@" & maildom
                	End If
 					mailaddress = account & maildom
				End If
			End If

			If $Mail[o]$ <> mailaddress AndAlso mailaddress <> "" Then 
				'make the new mailaddress unique by appending a counter
				Dim mailParts As String() = mailaddress.Split("@"c)

				Dim i as Integer = 0
		        Dim f As ISqlFormatter = Connection.SqlFormatter

		        While Connection.Exists("ADSAccount", _
									f.AndRelation( _
											f.Uid<PERSON>omparison("UID_ADSAccount", $UID_ADSAccount$, CompareOperator.NotEqual), _
											f.Comparison("Mail", mailaddress, ValType.String, CompareOperator.Equal, FormatterOptions.None) _
												)) _
						OrElse Connection.Exists("ADSContact", _
									f.Comparison("Mail", mailaddress, ValType.String, CompareOperator.Equal, FormatterOptions.None) _
												)
					i += 1
					mailaddress = mailParts(0) & i.ToString() & "@" & mailParts(1)
					If mailaddress.Length > 255 Then 
						mailaddress = mailParts(0).Substring(0, 255-i.ToString().Length) & i.ToString() & "@" & mailParts(1)
					End If
					
					If i = 100000 'termination
						Exit While
					End If

				End While
			
				Value = mailaddress

			End If
#End If	
		Case -1:'fill property initially from the employee
			If Not $[IsLoaded]:Bool$ Then
				if $IsPrivilegedAccount:Bool$ Then
					Value = ""
				else
					Value = $FK(UID_Person).DefaultEMailAddress$.ToLower
				end if
			End If
		Case 1:'update property depending on employee
			if $IsPrivilegedAccount:Bool$ Then
				Value = ""
			else
				Value = $FK(UID_Person).DefaultEMailAddress$.ToLower
			end if
	End Select
End If